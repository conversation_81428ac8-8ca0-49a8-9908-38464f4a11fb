import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { Database } from '@/lib/supabase/database.types';
import { UserRole } from '@/lib/types'; // Assuming UserRole type is defined here
import { isSuperAdminEmail } from '@/lib/auth/constants';

// This page is a server component to handle redirection server-side
export default async function PostLoginPage() {
  const cookieStore = await cookies(); // Await the cookies() call
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          // Setting cookies might be needed if the token needs refreshing during getUser
          try {
             cookieStore.set({ name, value, ...options });
          } catch (error) {
            console.error('Failed to set cookie in post-login route:', error);
          }
        },
        remove(name: string, options: CookieOptions) {
           try {
             cookieStore.delete({ name, ...options });
           } catch (error) {
            console.error('Failed to remove cookie in post-login route:', error);
          }
        }
      },
    }
  );

  const { data: { user }, error } = await supabase.auth.getUser();

  // Enhanced debugging for post-login issues
  console.log('POST-LOGIN DEBUG:', {
    hasUser: !!user,
    userEmail: user?.email,
    error: error?.message,
    userMetadata: user?.user_metadata,
    appMetadata: user?.app_metadata,
    timestamp: new Date().toISOString()
  });

  if (error || !user) {
    // Handle error or no user found - redirect back to login
    console.error('POST-LOGIN ERROR: No user found or error occurred:', {
      error: error?.message,
      hasUser: !!user,
      redirectingTo: '/login'
    });
    return redirect('/login'); // Use return redirect()
  }

  // Check for Superadmin emails using centralized list
  const isEmailSuperAdmin = isSuperAdminEmail(user.email);

  console.log('SUPERADMIN CHECK:', {
    userEmail: user.email,
    isEmailSuperAdmin,
    userMetadata: user.user_metadata,
    appMetadata: user.app_metadata
  });

  if (isEmailSuperAdmin) {
    console.log(`POST-LOGIN: Superadmin ${user.email} redirected to /superadmin`);
    return redirect('/superadmin');
  }

  // Check for superadmin status in metadata (fallback)
  const isSuperAdmin = user.user_metadata?.is_super_admin === true || user.app_metadata?.is_super_admin === true;
  if (isSuperAdmin) {
    console.log(`POST-LOGIN: Superadmin ${user.email} (via metadata) redirected to /superadmin`);
    return redirect('/superadmin');
  }

  // Get user role for logging purposes
  const userRole = user.app_metadata?.role || user.user_metadata?.role as UserRole;

  // Per user preference: redirect all authenticated users to Dashboard regardless of role
  // (except superadmins who are handled above)
  console.log(`User ${user.email} with role ${userRole} redirected to /dashboard (per user preference)`);
  return redirect('/dashboard');

  // Component must return JSX or null, although unreachable due to redirects
  // return null;
}
