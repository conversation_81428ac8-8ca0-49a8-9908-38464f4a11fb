import { NextRequest, NextResponse } from 'next/server';
import { with<PERSON><PERSON>, Auth<PERSON><PERSON>, User<PERSON><PERSON> } from '@/lib/auth/server-exports';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/database.types';

/**
 * Represents a processed, actionable insight derived from user activities.
 */
interface Insight {
  id: string;
  message: string;
  suggestions: string[];
  priority: number;
  relatedEntity?: {
    type: string;
    id?: string;
    name?: string | null;
  };
  timestamp: string;
  groupKey?: string;
  relatedActivities?: string[];
  feedbackId?: string;
  aiGenerated?: boolean;
}

/**
 * API response format for activity insights
 */
interface ActivityInsightsApiResponse {
  insights: Insight[];
  meta: {
    aiGenerated: boolean;
    totalCount: number;
    daysBack: number;
    useAi: boolean;
  };
}

/**
 * GET: Fetch activity insights for the current tenant
 * 
 * Query parameters:
 * - daysBack: Number of days to look back (default: 7)
 * - limit: Maximum number of insights to return (default: 15)
 * - useAi: Whether to use AI for generating insights (default: true)
 */
export const GET = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff],
  async (
    req: NextRequest,
    user: AuthUser,
    supabase: SupabaseClient<Database>
  ) => {
    try {
      const { searchParams } = new URL(req.url);
      const daysBack = parseInt(searchParams.get('daysBack') || '7');
      const limit = parseInt(searchParams.get('limit') || '15');
      const useAi = searchParams.get('useAi') === 'true';

      // For now, return empty insights to prevent 404 errors
      // TODO: Implement actual insights generation
      const response: ActivityInsightsApiResponse = {
        insights: [],
        meta: {
          aiGenerated: useAi,
          totalCount: 0,
          daysBack,
          useAi
        }
      };

      return NextResponse.json(response);
    } catch (error) {
      console.error('Error fetching activity insights:', error);
      return NextResponse.json(
        { error: 'Failed to fetch activity insights' },
        { status: 500 }
      );
    }
  }
);

/**
 * POST: Submit feedback on insights
 */
export const POST = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff],
  async (
    req: NextRequest,
    user: AuthUser,
    supabase: SupabaseClient<Database>
  ) => {
    try {
      const body = await req.json();
      
      // For now, just acknowledge the feedback
      // TODO: Implement actual feedback processing
      console.log('Insight feedback received:', body);

      return NextResponse.json({ success: true });
    } catch (error) {
      console.error('Error processing insight feedback:', error);
      return NextResponse.json(
        { error: 'Failed to process feedback' },
        { status: 500 }
      );
    }
  }
);
