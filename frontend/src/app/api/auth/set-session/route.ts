import { createRouteHandlerClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'

/**
 * POST /api/auth/set-session
 *
 * Persists the Supabase session as http-only cookies so that
 * Next.js middleware / server components can authenticate the user.
 * The client must send `{ access_token, refresh_token }` in the request body.
 */
export async function POST (request: Request) {
  try {
    const { access_token, refresh_token } = await request.json() as { access_token?: string, refresh_token?: string }
    if (!access_token || !refresh_token) {
      return NextResponse.json({ error: 'Missing tokens' }, { status: 400 })
    }

    const supabase = createRouteHandlerClient()
    const { error } = await supabase.auth.setSession({ access_token, refresh_token })
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 })
    }

    return NextResponse.json({ success: true })
  } catch (err: unknown) {
    console.error('set-session error', err)
    return NextResponse.json({ error: 'Unexpected error' }, { status: 500 })
  }
}
