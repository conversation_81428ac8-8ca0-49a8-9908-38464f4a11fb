{"version": 4, "terraform_version": "1.5.7", "serial": 67, "lineage": "ca5901af-c120-8608-db7b-51fbfa241a41", "outputs": {"mcp_api_key_names": {"value": {"demo-tenant": "mcp-rules-api-demo-tenant", "pilot-smith": "mcp-rules-api-pilot-smith"}, "type": ["object", {"demo-tenant": "string", "pilot-smith": "string"}]}, "tenant_onboard_function_name": {"value": "tenant-onboard", "type": "string"}}, "resources": [{"mode": "data", "type": "archive_file", "name": "function_source", "provider": "provider[\"registry.terraform.io/hashicorp/archive\"]", "instances": [{"schema_version": 0, "attributes": {"exclude_symlink_directories": null, "excludes": ["*.log", "*.zip", ".git", "lib", "node_modules"], "id": "fa9835404a128608455f219c697c66da20758533", "output_base64sha256": "+92dJ8fJFlttRCfUjsWHEsyS3EBxwBt9bZwpeZCZUEI=", "output_base64sha512": "KAKbavvq/x7zVayBdwdRHYTiw5UuQ4wTwEzXZXnJ1iKPFZQQRNcnkegwsrOqs0vdsC2GV/jO8Sbj0+mxWdW5Tw==", "output_file_mode": null, "output_md5": "90a3fcc8388b1f1f336170146015f91e", "output_path": "./../../cloud-functions/function-source.zip", "output_sha": "fa9835404a128608455f219c697c66da20758533", "output_sha256": "fbdd9d27c7c9165b6d4427d48ec58712cc92dc4071c01b7d6d9c297990995042", "output_sha512": "28029b6afbeaff1ef355ac817707511d84e2c3952e438c13c04cd76579c9d6228f15941044d72791e830b2b3aab34bddb02d8657f8cef126e3d3e9b159d5b94f", "output_size": 3915, "source": [], "source_content": null, "source_content_filename": null, "source_dir": "./../../cloud-functions", "source_file": null, "type": "zip"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "google_apikeys_key", "name": "mcp_api_keys", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_cloud_run_service_iam_member", "name": "function_invoker", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].tenant_project", "instances": []}, {"mode": "managed", "type": "google_cloudfunctions2_function", "name": "tenant_onboard", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].tenant_project", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "function_apikeys_admin", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "function_firestore_user", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].tenant_project", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "function_run_invoker", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].tenant_project", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "function_secretmanager_admin", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "mcp_key_rotator_binding", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "provisioner_apikeys_admin", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].mcp_project", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "provisioner_secretmanager_admin", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].mcp_project", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret", "name": "mcp_api_keys", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret_iam_binding", "name": "mcp_secret_accessor", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret_version", "name": "mcp_api_key_versions", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret_version", "name": "mcp_initial_versions", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret_version", "name": "rotator_sa_key_version", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_service_account_iam_member", "name": "allow_impersonation", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].mcp_project", "instances": []}, {"mode": "managed", "type": "google_service_account_key", "name": "mcp_key_rotator_key", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_storage_bucket", "name": "function_source", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].tenant_project", "instances": [{"schema_version": 2, "attributes": {"autoclass": [], "cors": [], "custom_placement_config": [], "default_event_based_hold": false, "effective_labels": {}, "enable_object_retention": false, "encryption": [], "force_destroy": false, "id": "texas-laws-personalinjury-tenant-onboard-functions", "labels": {}, "lifecycle_rule": [{"action": [{"storage_class": "", "type": "Delete"}], "condition": [{"age": 30, "created_before": "", "custom_time_before": "", "days_since_custom_time": 0, "days_since_noncurrent_time": 0, "matches_prefix": [], "matches_storage_class": [], "matches_suffix": [], "no_age": false, "noncurrent_time_before": "", "num_newer_versions": 0, "send_age_if_zero": true, "send_days_since_custom_time_if_zero": false, "send_days_since_noncurrent_time_if_zero": false, "send_num_newer_versions_if_zero": false, "with_state": "ANY"}]}], "location": "US-CENTRAL1", "logging": [], "name": "texas-laws-personalinjury-tenant-onboard-functions", "project": "texas-laws-personalinjury", "project_number": 122290401475, "public_access_prevention": "inherited", "requester_pays": false, "retention_policy": [], "rpo": null, "self_link": "https://www.googleapis.com/storage/v1/b/texas-laws-personalinjury-tenant-onboard-functions", "soft_delete_policy": [{"effective_time": "2025-06-19T09:44:10.520Z", "retention_duration_seconds": 604800}], "storage_class": "STANDARD", "terraform_labels": {}, "timeouts": null, "uniform_bucket_level_access": true, "url": "gs://texas-laws-personalinjury-tenant-onboard-functions", "versioning": [{"enabled": true}], "website": []}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsInJlYWQiOjI0MDAwMDAwMDAwMCwidXBkYXRlIjoyNDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ=="}]}, {"mode": "managed", "type": "google_storage_bucket_object", "name": "function_source", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].tenant_project", "instances": [{"schema_version": 0, "attributes": {"bucket": "texas-laws-personalinjury-tenant-onboard-functions", "cache_control": "", "content": null, "content_disposition": "", "content_encoding": "", "content_language": "", "content_type": "application/zip", "crc32c": "bIzQ5w==", "customer_encryption": [], "detect_md5hash": "kKP8yDiLHx8zYXAUYBX5Hg==", "event_based_hold": false, "generation": 1750356573106249, "id": "texas-laws-personalinjury-tenant-onboard-functions-tenant-onboard-90a3fcc8388b1f1f336170146015f91e.zip", "kms_key_name": "", "md5hash": "kKP8yDiLHx8zYXAUYBX5Hg==", "media_link": "https://storage.googleapis.com/download/storage/v1/b/texas-laws-personalinjury-tenant-onboard-functions/o/tenant-onboard-90a3fcc8388b1f1f336170146015f91e.zip?generation=1750356573106249&alt=media", "metadata": {}, "name": "tenant-onboard-90a3fcc8388b1f1f336170146015f91e.zip", "output_name": "tenant-onboard-90a3fcc8388b1f1f336170146015f91e.zip", "retention": [], "self_link": "https://www.googleapis.com/storage/v1/b/texas-laws-personalinjury-tenant-onboard-functions/o/tenant-onboard-90a3fcc8388b1f1f336170146015f91e.zip", "source": "./../../cloud-functions/function-source.zip", "storage_class": "STANDARD", "temporary_hold": false, "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoyNDAwMDAwMDAwMDAsImRlbGV0ZSI6MjQwMDAwMDAwMDAwLCJ1cGRhdGUiOjI0MDAwMDAwMDAwMH19", "dependencies": ["data.archive_file.function_source", "google_storage_bucket.function_source"]}]}], "check_results": null}